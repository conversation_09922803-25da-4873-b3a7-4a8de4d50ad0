#!/usr/bin/env python3
"""
初始化上传目录脚本

确保所有必要的上传目录都存在并具有正确的权限
"""
import os
import sys
import stat
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.config import settings


def create_upload_directories():
    """创建所有必要的上传目录"""
    
    # 需要创建的目录列表
    directories = [
        settings.upload_dir_absolute,
        os.path.join(settings.upload_dir_absolute, "temp"),
        os.path.join(settings.upload_dir_absolute, "temp", "excel"),
        os.path.join(settings.upload_dir_absolute, "rankings"),
        os.path.join(settings.upload_dir_absolute, "avatars"),
        os.path.join(settings.upload_dir_absolute, "exports"),
    ]
    
    created_dirs = []
    failed_dirs = []
    
    for directory in directories:
        try:
            # 创建目录
            Path(directory).mkdir(parents=True, exist_ok=True)
            
            # 设置权限 (755: rwxr-xr-x)
            os.chmod(directory, stat.S_IRWXU | stat.S_IRGRP | stat.S_IXGRP | stat.S_IROTH | stat.S_IXOTH)
            
            created_dirs.append(directory)
            print(f"✓ 创建目录: {directory}")
            
        except PermissionError as e:
            failed_dirs.append((directory, str(e)))
            print(f"✗ 创建目录失败: {directory} - {str(e)}")
        except Exception as e:
            failed_dirs.append((directory, str(e)))
            print(f"✗ 创建目录失败: {directory} - {str(e)}")
    
    return created_dirs, failed_dirs


def check_directory_permissions():
    """检查目录权限"""
    upload_dir = settings.upload_dir_absolute
    
    if not os.path.exists(upload_dir):
        print(f"✗ 上传目录不存在: {upload_dir}")
        return False
    
    # 检查读写权限
    if not os.access(upload_dir, os.R_OK):
        print(f"✗ 上传目录无读权限: {upload_dir}")
        return False
    
    if not os.access(upload_dir, os.W_OK):
        print(f"✗ 上传目录无写权限: {upload_dir}")
        return False
    
    print(f"✓ 上传目录权限正常: {upload_dir}")
    return True


def main():
    """主函数"""
    print("🚀 初始化上传目录...")
    print(f"📁 上传根目录: {settings.upload_dir_absolute}")
    print()
    
    # 创建目录
    created_dirs, failed_dirs = create_upload_directories()
    
    print()
    print("📊 创建结果:")
    print(f"✓ 成功创建: {len(created_dirs)} 个目录")
    print(f"✗ 创建失败: {len(failed_dirs)} 个目录")
    
    if failed_dirs:
        print("\n❌ 失败的目录:")
        for directory, error in failed_dirs:
            print(f"  - {directory}: {error}")
    
    print()
    
    # 检查权限
    print("🔍 检查目录权限...")
    if check_directory_permissions():
        print("✅ 目录权限检查通过")
    else:
        print("❌ 目录权限检查失败")
        return 1
    
    print()
    print("🎉 上传目录初始化完成！")
    return 0


if __name__ == "__main__":
    sys.exit(main())
