#!/usr/bin/env python3
"""
测试Content模型重构后的功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime
from pydantic import ValidationError

# 测试模型导入
def test_model_imports():
    """测试模型导入是否正常"""
    print("🔍 测试模型导入...")
    try:
        from app.models.content import Content, ContentType, BroadcastMessage
        print("   ✅ Content模型导入成功")
        print("   ✅ ContentType枚举导入成功")
        print("   ✅ BroadcastMessage模型导入成功")
        return True
    except ImportError as e:
        print(f"   ❌ 模型导入失败: {e}")
        return False

# 测试Schema导入
def test_schema_imports():
    """测试Schema导入是否正常"""
    print("\n🔍 测试Schema导入...")
    try:
        from app.schemas.content import ContentBase, ContentCreate, ContentUpdate, ContentResponse
        print("   ✅ ContentBase schema导入成功")
        print("   ✅ ContentCreate schema导入成功")
        print("   ✅ ContentUpdate schema导入成功")
        print("   ✅ ContentResponse schema导入成功")
        return True
    except ImportError as e:
        print(f"   ❌ Schema导入失败: {e}")
        return False

# 测试Schema验证
def test_schema_validation():
    """测试Schema验证功能"""
    print("\n🔍 测试Schema验证...")
    try:
        from app.schemas.content import ContentCreate, ContentUpdate
        
        # 测试ContentCreate
        valid_data = {
            "content_type": "announcement",
            "title": "测试公告",
            "content": "这是一个测试公告内容",
            "is_published": True,
            "publish_at": datetime.now()
        }
        
        content_create = ContentCreate(**valid_data)
        print("   ✅ ContentCreate验证成功")
        
        # 测试ContentUpdate
        update_data = {
            "title": "更新后的标题",
            "is_published": False
        }
        
        content_update = ContentUpdate(**update_data)
        print("   ✅ ContentUpdate验证成功")
        
        # 测试必填字段验证
        try:
            invalid_data = {"title": "只有标题"}  # 缺少content_type
            ContentCreate(**invalid_data)
            print("   ❌ 必填字段验证失败")
            return False
        except ValidationError:
            print("   ✅ 必填字段验证成功")
        
        return True
    except Exception as e:
        print(f"   ❌ Schema验证失败: {e}")
        return False

# 测试服务导入
def test_service_imports():
    """测试服务导入是否正常"""
    print("\n🔍 测试服务导入...")
    try:
        from app.services.content_service import ContentService
        service = ContentService()
        print("   ✅ ContentService导入和实例化成功")
        return True
    except ImportError as e:
        print(f"   ❌ 服务导入失败: {e}")
        return False

# 测试API导入
def test_api_imports():
    """测试API导入是否正常"""
    print("\n🔍 测试API导入...")
    try:
        from app.api.v1.endpoints.content import router
        print("   ✅ Content API路由导入成功")
        return True
    except ImportError as e:
        print(f"   ❌ API导入失败: {e}")
        return False

# 测试模型字段
def test_model_fields():
    """测试模型字段定义"""
    print("\n🔍 测试模型字段...")
    try:
        from app.models.content import Content
        
        # 检查新字段是否存在
        expected_fields = [
            'id', 'content_type', 'title', 'content', 
            'is_published', 'publish_at', 'created_at', 'updated_at'
        ]
        
        for field in expected_fields:
            if hasattr(Content, field):
                print(f"   ✅ 字段 {field} 存在")
            else:
                print(f"   ❌ 字段 {field} 不存在")
                return False
        
        # 检查已删除的字段是否不存在
        removed_fields = [
            'status', 'summary', 'keywords', 'is_featured', 
            'is_top', 'display_order', 'view_count', 'publish_time',
            'expire_time', 'created_by', 'updated_by'
        ]
        
        for field in removed_fields:
            if not hasattr(Content, field):
                print(f"   ✅ 已删除字段 {field} 不存在")
            else:
                print(f"   ❌ 字段 {field} 仍然存在（应该已删除）")
                return False
        
        return True
    except Exception as e:
        print(f"   ❌ 模型字段测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试Content模型重构...")
    
    tests = [
        test_model_imports,
        test_schema_imports,
        test_schema_validation,
        test_service_imports,
        test_api_imports,
        test_model_fields
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Content模型重构成功！")
        return True
    else:
        print("❌ 部分测试失败，请检查相关问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
