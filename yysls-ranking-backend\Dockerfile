# 燕友圈榜单系统 - 生产环境Docker镜像

# 使用官方Python 3.11 slim镜像作为基础镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app:/app/app \
    TZ=Asia/Shanghai

# 安装系统依赖
# 使用国内源，加速 apt
# 安装系统依赖（适配 slim）
RUN echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian bookworm main contrib non-free non-free-firmware" > /etc/apt/sources.list \
    && echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian bookworm-updates main contrib non-free non-free-firmware" >> /etc/apt/sources.list \
    && echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian-security bookworm-security main contrib non-free non-free-firmware" >> /etc/apt/sources.list \
    && apt update \
    && apt install -y gcc g++ libpq-dev curl \
    && rm -rf /var/lib/apt/lists/*


# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 复制pip配置和requirements文件
COPY pip.conf /etc/pip.conf
COPY requirements.txt .

# 安装Python依赖（使用国内源配置）
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要的目录和子目录
RUN mkdir -p /app/logs /app/uploads/temp/excel /app/static && \
    chown -R appuser:appuser /app && \
    chmod -R 755 /app/uploads /app/static

# 切换到非root用户
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "start.py"]
