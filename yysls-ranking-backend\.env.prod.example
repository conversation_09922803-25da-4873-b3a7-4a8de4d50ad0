# 生产环境配置文件模板
# 复制此文件为 .env.prod 并填入真实的配置值

# 应用配置
APP_NAME=燕友圈榜单系统
APP_VERSION=1.0.0
DEBUG=false
LOG_LEVEL=INFO

# 数据库配置 (MySQL 8)
DATABASE_URL=mysql+pymysql://root:your_password@localhost:3306/yysls_ranking?charset=utf8mb4
DATABASE_URL_ASYNC=mysql+aiomysql://root:your_password@localhost:3306/yysls_ranking?charset=utf8mb4
MYSQL_DATABASE=yysls_ranking
MYSQL_USER=root
MYSQL_PASSWORD=your_strong_password
MYSQL_ROOT_PASSWORD=your_root_password

# JWT配置
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 微信配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# 邮件配置
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_email_password
SMTP_USE_TLS=true
FROM_EMAIL=<EMAIL>
FROM_NAME=燕友圈榜单系统

# 前端配置
FRONTEND_URL=https://your-domain.com

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 文件上传配置
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=10485760  # 10MB

# 注意：在Docker环境中，UPLOAD_DIR应该设置为容器内的绝对路径
# 确保该目录在容器启动时具有正确的权限

# 安全配置
ALLOWED_HOSTS=your-domain.com,www.your-domain.com
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com

# 监控配置
SENTRY_DSN=your_sentry_dsn_if_using_sentry
