"""
内容管理模型
"""
from datetime import datetime
from enum import Enum

from sqlalchemy import Boolean, Column, DateTime, ForeignKey, Index, Integer, String, Text
from sqlalchemy.orm import relationship

from app.core.database import Base


class ContentType(str, Enum):
    """内容类型枚举"""
    BROADCAST = "broadcast"      # 播报信息
    NOTIFY = "notify"           # 通知播报
    ANNOUNCEMENT = "announcement"  # 公告
    ABOUT = "about"             # 关于我们
    HELP = "help"               # 帮助文档
    PRIVACY = "privacy"         # 隐私政策
    TERMS = "terms"             # 服务条款


class Content(Base):
    """内容表 - 按照MySQL表结构重构"""
    __tablename__ = "contents"

    # 主键，自增整数
    id = Column(Integer, primary_key=True, autoincrement=True)

    # 内容类型，varchar(50)，非空
    content_type = Column(String(50), nullable=False, comment="内容类型")

    # 标题，varchar(200)，非空
    title = Column(String(200), nullable=False, comment="标题")

    # 内容正文，text类型，可为空
    content = Column(Text, nullable=True, comment="内容正文")

    # 发布状态，布尔值，默认false
    is_published = Column(Boolean, default=False, nullable=False, comment="发布状态")

    # 发布时间，timestamp，可为空
    publish_at = Column(DateTime, nullable=True, comment="发布时间")

    # 创建时间，timestamp，默认当前时间
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")

    # 更新时间，timestamp，默认当前时间且自动更新
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="更新时间")

    # 定义索引
    __table_args__ = (
        Index('idx_content_type', 'content_type'),
        Index('idx_created_at', 'created_at'),
        Index('idx_is_published', 'is_published'),
        Index('idx_publish_at', 'publish_at'),
    )

    def __repr__(self):
        return f"<Content(id={self.id}, title='{self.title}', type='{self.content_type}', published={self.is_published})>"


class BroadcastMessage(Base):
    """播报消息表"""
    __tablename__ = "broadcast_messages"

    id = Column(Integer, primary_key=True, index=True)
    
    # 消息内容
    message = Column(Text, nullable=False, comment="播报消息")
    message_type = Column(String(50), default="info", nullable=False, comment="消息类型")
    
    # 显示配置
    display_duration = Column(Integer, default=15, nullable=False, comment="显示时长(秒)")
    display_order = Column(Integer, default=0, nullable=False, comment="显示顺序")
    
    # 状态信息
    is_active = Column(Boolean, default=True, nullable=False, comment="是否启用")
    
    # 时间配置
    start_time = Column(DateTime, nullable=True, comment="开始时间")
    end_time = Column(DateTime, nullable=True, comment="结束时间")
    
    # 管理信息
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False, comment="创建人ID")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="更新时间")
    
    # 关系
    creator = relationship("User", backref="broadcast_messages")
    
    def __repr__(self):
        return f"<BroadcastMessage(id={self.id}, message='{self.message[:50]}...', is_active={self.is_active})>"
