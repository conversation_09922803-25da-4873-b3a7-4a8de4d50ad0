#!/usr/bin/env python3
"""
应用启动脚本 - 用于Docker容器
"""
import sys
import os

# 确保当前目录在Python路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 确保app目录在Python路径中
app_dir = os.path.join(current_dir, 'app')
if app_dir not in sys.path:
    sys.path.insert(0, app_dir)

# 设置环境变量
os.environ.setdefault('PYTHONPATH', f"{current_dir}:{app_dir}")

def init_upload_directories():
    """初始化上传目录"""
    try:
        from app.config import settings
        import stat
        from pathlib import Path

        # 需要创建的目录列表
        directories = [
            # 上传目录
            settings.upload_dir_absolute,
            os.path.join(settings.upload_dir_absolute, "temp"),
            os.path.join(settings.upload_dir_absolute, "temp", "excel"),
            os.path.join(settings.upload_dir_absolute, "rankings"),
            os.path.join(settings.upload_dir_absolute, "avatars"),
            os.path.join(settings.upload_dir_absolute, "exports"),
            # 静态文件目录
            settings.static_dir_absolute,
        ]

        for directory in directories:
            try:
                # 创建目录
                Path(directory).mkdir(parents=True, exist_ok=True)

                # 设置权限 (755: rwxr-xr-x)
                os.chmod(directory, stat.S_IRWXU | stat.S_IRGRP | stat.S_IXGRP | stat.S_IROTH | stat.S_IXOTH)

                print(f"✓ 初始化目录: {directory}")

            except Exception as e:
                print(f"✗ 初始化目录失败: {directory} - {str(e)}")

    except Exception as e:
        print(f"✗ 初始化上传目录失败: {str(e)}")


if __name__ == "__main__":
    import uvicorn

    # 初始化目录
    print("🚀 初始化文件目录...")
    init_upload_directories()
    print("📁 文件目录初始化完成")

    # 启动应用
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        workers=1,  # 减少worker数量避免日志配置冲突
        log_level="info"
    )
