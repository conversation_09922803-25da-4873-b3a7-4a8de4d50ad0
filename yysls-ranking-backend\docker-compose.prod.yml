version: '3.8'

services:
  # 应用服务
  app:
    build: .
    container_name: yysls-ranking-app
    restart: unless-stopped
    # 移除端口映射，只通过nginx访问
    expose:
      - "8000"
    env_file:
      - .env.prod
    environment:
      - DATABASE_URL=mysql+pymysql://root:${MYSQL_ROOT_PASSWORD}@db:3306/${MYSQL_DATABASE}?charset=utf8mb4
      - DATABASE_URL_ASYNC=mysql+aiomysql://root:${MYSQL_ROOT_PASSWORD}@db:3306/${MYSQL_DATABASE}?charset=utf8mb4
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs
      - uploads_data:/app/uploads
      - static_data:/app/static
    networks:
      - yysls-network

  # 数据库服务
  db:
    image: mysql:8.0
    container_name: yysls-ranking-db
    restart: unless-stopped
    env_file:
      - .env.prod
    environment:
      - MYSQL_DATABASE=${MYSQL_DATABASE}
      - MYSQL_USER=${MYSQL_USER}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d
    ports:
      - "3306:3306"
    networks:
      - yysls-network

  # Redis服务
  redis:
    image: redis:7-alpine
    container_name: yysls-ranking-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    networks:
      - yysls-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: yysls-ranking-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      # SSL证书挂载（生产环境中应使用外部卷或密钥管理）
      - ./ssl:/etc/nginx/ssl:ro
      - uploads_data:/app/uploads:ro
      - static_data:/app/static:ro
      - nginx_logs:/var/log/nginx
    environment:
      - TZ=Asia/Shanghai
    depends_on:
      - app
    networks:
      - yysls-network

volumes:
  mysql_data:
  redis_data:
  nginx_logs:
  uploads_data:
  static_data:

networks:
  yysls-network:
    driver: bridge
