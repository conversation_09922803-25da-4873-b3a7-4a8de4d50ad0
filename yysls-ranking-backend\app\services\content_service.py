"""
内容管理业务服务

处理内容管理相关的业务逻辑，包括：
- 内容发布和管理
- 播报消息管理
- 内容状态管理
- 内容统计
"""
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from sqlalchemy import select, and_, or_, func
from datetime import datetime

from app.services.base import BaseService
from app.models.content import Content, BroadcastMessage, ContentType


class ContentService(BaseService):
    """内容管理服务类"""
    
    def __init__(self):
        super().__init__(Content)
    
    async def get_by_type(
        self,
        db: AsyncSession,
        content_type: ContentType,
        is_published: bool = True,
        skip: int = 0,
        limit: int = 100
    ) -> List[Content]:
        """根据类型获取内容列表"""
        try:
            query = select(Content).where(
                and_(
                    Content.content_type == content_type,
                    Content.is_published == is_published
                )
            )

            # 按发布时间排序
            query = query.order_by(
                Content.publish_at.desc(),
                Content.created_at.desc()
            ).offset(skip).limit(limit)
            
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            self.logger.error(f"根据类型获取内容失败 type={content_type}: {str(e)}")
            raise
    
    async def create_content(
        self,
        db: AsyncSession,
        content_data: Dict[str, Any]
    ) -> Content:
        """创建内容"""
        try:
            # 如果是发布状态且没有设置发布时间，设置为当前时间
            if content_data.get('is_published') and not content_data.get('publish_at'):
                content_data['publish_at'] = datetime.utcnow()

            db_content = Content(**content_data)
            db.add(db_content)
            await db.commit()
            await db.refresh(db_content)

            self.logger.info(f"创建内容成功 ID={db_content.id}, title={db_content.title}")
            return db_content
        except Exception as e:
            await db.rollback()
            self.logger.error(f"创建内容失败: {str(e)}")
            raise
    
    async def publish_content(
        self,
        db: AsyncSession,
        content_id: int
    ) -> Content:
        """发布内容"""
        try:
            content = await self.get(db, content_id)
            if not content:
                raise ValueError(f"内容不存在 ID={content_id}")

            content.is_published = True
            content.publish_at = datetime.utcnow()

            await db.commit()
            await db.refresh(content)

            self.logger.info(f"发布内容成功 ID={content_id}")
            return content
        except Exception as e:
            await db.rollback()
            self.logger.error(f"发布内容失败 ID={content_id}: {str(e)}")
            raise
    
    async def get_about_content(self, db: AsyncSession) -> Optional[Content]:
        """获取关于我们内容"""
        try:
            result = await db.execute(
                select(Content).where(
                    and_(
                        Content.content_type == ContentType.ABOUT,
                        Content.is_published == True
                    )
                ).order_by(Content.updated_at.desc())
            )
            return result.scalar_one_or_none()
        except Exception as e:
            self.logger.error(f"获取关于我们内容失败: {str(e)}")
            raise

    def get_about_content_sync(self, db: Session) -> Optional[Content]:
        """获取关于我们内容 - 同步版本"""
        try:
            result = db.execute(
                select(Content).where(
                    and_(
                        Content.content_type == ContentType.ABOUT,
                        Content.is_published == True
                    )
                ).order_by(Content.updated_at.desc())
            )
            return result.scalar_one_or_none()
        except Exception as e:
            self.logger.error(f"获取关于我们内容失败: {str(e)}")
            raise
    


    async def search_contents(
        self,
        db: AsyncSession,
        search_term: str,
        skip: int = 0,
        limit: int = 100,
        **filters
    ) -> tuple[List[Content], int]:
        """搜索内容（返回结果和总数）"""
        try:
            # 构建搜索条件
            search_conditions = or_(
                Content.title.ilike(f"%{search_term}%"),
                Content.content.ilike(f"%{search_term}%"),
                Content.summary.ilike(f"%{search_term}%")
            )

            # 构建过滤条件
            filter_conditions = self._build_filters(**filters)

            # 组合所有条件
            all_conditions = [search_conditions]
            if filter_conditions:
                all_conditions.extend(filter_conditions)

            # 构建查询
            query = select(Content).where(and_(*all_conditions))
            count_query = select(func.count(Content.id)).where(and_(*all_conditions))

            # 获取总数
            total_result = await db.execute(count_query)
            total = total_result.scalar()

            # 获取数据
            query = query.order_by(
                Content.publish_at.desc(),
                Content.created_at.desc()
            ).offset(skip).limit(limit)
            result = await db.execute(query)
            contents = result.scalars().all()

            return contents, total
        except Exception as e:
            self.logger.error(f"搜索内容失败 search_term={search_term}: {str(e)}")
            raise

    async def get_published_announcements(self, db: AsyncSession, limit: int = 10) -> List[Content]:
        """获取已发布的公告列表"""
        try:
            result = await db.execute(
                select(Content).where(
                    and_(
                        Content.content_type == ContentType.ANNOUNCEMENT,
                        Content.is_published == True
                    )
                ).order_by(
                    Content.publish_at.desc(),
                    Content.created_at.desc()
                ).limit(limit)
            )
            return result.scalars().all()
        except Exception as e:
            self.logger.error(f"获取公告列表失败: {str(e)}")
            raise

    def get_published_announcements_sync(self, db: Session, limit: int = 10) -> List[Content]:
        """获取已发布的公告列表 - 同步版本"""
        try:
            result = db.execute(
                select(Content).where(
                    and_(
                        Content.content_type == ContentType.ANNOUNCEMENT,
                        Content.is_published == True
                    )
                ).order_by(
                    Content.publish_at.desc(),
                    Content.created_at.desc()
                ).limit(limit)
            )
            return result.scalars().all()
        except Exception as e:
            self.logger.error(f"获取公告列表失败: {str(e)}")
            raise

    async def get_broadcast_messages(
        self,
        db: AsyncSession,
        limit: int = 10,
        **filters
    ) -> List[Content]:
        """获取播报消息列表 - 从content表查询notify类型的数据"""
        try:
            # 构建查询条件
            query_conditions = [Content.content_type == ContentType.NOTIFY]

            # 处理is_active过滤条件，映射到is_published字段
            if 'is_active' in filters and filters['is_active'] is not None:
                query_conditions.append(Content.is_published == filters['is_active'])

            # 构建查询
            query = select(Content).where(and_(*query_conditions))

            # 按发布时间和创建时间排序
            query = query.order_by(
                Content.publish_at.desc(),
                Content.created_at.desc()
            ).limit(limit)

            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            self.logger.error(f"获取播报消息列表失败: {str(e)}")
            raise

    def get_broadcast_messages_sync(
        self,
        db: Session,
        limit: int = 10,
        **filters
    ) -> List[Content]:
        """获取播报消息列表 - 同步版本，从content表查询notify类型的数据"""
        try:
            # 构建查询条件
            query_conditions = [Content.content_type == ContentType.NOTIFY]

            # 处理is_active过滤条件，映射到is_published字段
            if 'is_active' in filters and filters['is_active'] is not None:
                query_conditions.append(Content.is_published == filters['is_active'])

            # 构建查询
            query = select(Content).where(and_(*query_conditions))

            # 按发布时间和创建时间排序
            query = query.order_by(
                Content.publish_at.desc(),
                Content.created_at.desc()
            ).limit(limit)

            result = db.execute(query)
            return result.scalars().all()
        except Exception as e:
            self.logger.error(f"获取播报消息列表失败: {str(e)}")
            raise

    def convert_content_to_broadcast_message(self, content: Content) -> Dict[str, Any]:
        """将Content对象转换为BroadcastMessageResponse格式的字典"""
        return {
            "id": content.id,
            "title": content.title,
            "content": content.content or "",
            "message_type": "notify",  # 固定为notify类型
            "priority": 0,  # 默认优先级
            "is_active": content.is_published,  # 映射发布状态到激活状态
            "start_time": content.publish_at,  # 映射发布时间到开始时间
            "end_time": None,  # 默认无结束时间
            "display_duration": 5,  # 默认显示5秒
            "target_audience": None,  # 默认无目标受众
            "created_by": None,  # Content表没有创建者字段
            "updated_by": None,  # Content表没有更新者字段
            "created_at": content.created_at,
            "updated_at": content.updated_at
        }

    async def create_broadcast_message(
        self,
        db: AsyncSession,
        obj_in: Dict[str, Any]
    ) -> BroadcastMessage:
        """创建播报消息"""
        try:
            db_message = BroadcastMessage(**obj_in)
            db.add(db_message)
            await db.commit()
            await db.refresh(db_message)

            self.logger.info(f"创建播报消息成功 ID={db_message.id}")
            return db_message
        except Exception as e:
            await db.rollback()
            self.logger.error(f"创建播报消息失败: {str(e)}")
            raise


class BroadcastService(BaseService):
    """播报消息服务类"""
    
    def __init__(self):
        super().__init__(BroadcastMessage)
    
    async def get_active_messages(self, db: AsyncSession) -> List[BroadcastMessage]:
        """获取当前活跃的播报消息"""
        try:
            now = datetime.utcnow()
            result = await db.execute(
                select(BroadcastMessage).where(
                    and_(
                        BroadcastMessage.is_active == True,
                        or_(
                            BroadcastMessage.start_time.is_(None),
                            BroadcastMessage.start_time <= now
                        ),
                        or_(
                            BroadcastMessage.end_time.is_(None),
                            BroadcastMessage.end_time >= now
                        )
                    )
                ).order_by(BroadcastMessage.display_order)
            )
            return result.scalars().all()
        except Exception as e:
            self.logger.error(f"获取活跃播报消息失败: {str(e)}")
            raise
    
    async def create_broadcast(
        self, 
        db: AsyncSession, 
        message_data: Dict[str, Any], 
        created_by: int
    ) -> BroadcastMessage:
        """创建播报消息"""
        try:
            message_data['created_by'] = created_by
            
            db_message = BroadcastMessage(**message_data)
            db.add(db_message)
            await db.commit()
            await db.refresh(db_message)
            
            self.logger.info(f"创建播报消息成功 ID={db_message.id}")
            return db_message
        except Exception as e:
            await db.rollback()
            self.logger.error(f"创建播报消息失败: {str(e)}")
            raise


# 创建全局服务实例
content_service = ContentService()
broadcast_service = BroadcastService()
