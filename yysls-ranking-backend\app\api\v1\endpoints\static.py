"""
静态文件管理API
"""
import os
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path

from fastapi import APIRouter, File, UploadFile, HTTPException, status, Depends, Query
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session

from app.config import settings
from app.core.deps import get_db, get_current_user_id
from app.schemas.response import ResponseModel
from app.utils.logger import get_logger

router = APIRouter()
logger = get_logger(__name__)

# 允许的文件类型
ALLOWED_IMAGE_TYPES = {
    "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp", "image/svg+xml"
}

ALLOWED_DOCUMENT_TYPES = {
    "application/pdf", "application/msword", 
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "text/plain", "text/csv"
}

# 文件大小限制 (10MB)
MAX_FILE_SIZE = 10 * 1024 * 1024


@router.post("/upload/image", response_model=ResponseModel[Dict[str, Any]], summary="上传图片文件")
async def upload_image(
    file: UploadFile = File(..., description="图片文件"),
    current_user_id: int = Depends(get_current_user_id),
    db: Session = Depends(get_db)
) -> ResponseModel[Dict[str, Any]]:
    """
    上传图片文件到静态文件目录
    
    - **file**: 图片文件 (支持 jpg, png, gif, webp, svg)
    
    返回文件访问URL和文件信息
    """
    try:
        # 验证文件类型
        if file.content_type not in ALLOWED_IMAGE_TYPES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的图片格式，支持格式: {', '.join(ALLOWED_IMAGE_TYPES)}"
            )
        
        # 验证文件大小
        file_content = await file.read()
        if len(file_content) > MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"文件大小超过限制，最大支持 {MAX_FILE_SIZE // (1024*1024)}MB"
            )
        
        # 创建图片目录
        images_dir = os.path.join(settings.static_dir_absolute, "images")
        os.makedirs(images_dir, exist_ok=True)
        
        # 生成唯一文件名
        file_extension = os.path.splitext(file.filename)[1]
        unique_filename = f"{uuid.uuid4().hex}_{current_user_id}{file_extension}"
        file_path = os.path.join(images_dir, unique_filename)
        
        # 保存文件
        with open(file_path, "wb") as f:
            f.write(file_content)
        
        # 生成访问URL
        file_url = f"{settings.static_url_path}/images/{unique_filename}"
        
        # 文件信息
        file_info = {
            "original_filename": file.filename,
            "filename": unique_filename,
            "file_path": file_path,
            "file_url": file_url,
            "file_size": len(file_content),
            "content_type": file.content_type,
            "uploaded_by": current_user_id,
            "uploaded_at": datetime.now().isoformat()
        }
        
        logger.info(f"用户{current_user_id}上传图片: {file.filename} -> {unique_filename}")
        
        return ResponseModel(
            code=200,
            message="图片上传成功",
            data=file_info
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传图片失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"上传图片失败: {str(e)}"
        )


@router.post("/upload/document", response_model=ResponseModel[Dict[str, Any]], summary="上传文档文件")
async def upload_document(
    file: UploadFile = File(..., description="文档文件"),
    current_user_id: int = Depends(get_current_user_id),
    db: Session = Depends(get_db)
) -> ResponseModel[Dict[str, Any]]:
    """
    上传文档文件到静态文件目录
    
    - **file**: 文档文件 (支持 pdf, doc, docx, xls, xlsx, txt, csv)
    
    返回文件访问URL和文件信息
    """
    try:
        # 验证文件类型
        if file.content_type not in ALLOWED_DOCUMENT_TYPES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的文档格式，支持格式: {', '.join(ALLOWED_DOCUMENT_TYPES)}"
            )
        
        # 验证文件大小
        file_content = await file.read()
        if len(file_content) > MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"文件大小超过限制，最大支持 {MAX_FILE_SIZE // (1024*1024)}MB"
            )
        
        # 创建文档目录
        documents_dir = os.path.join(settings.static_dir_absolute, "documents")
        os.makedirs(documents_dir, exist_ok=True)
        
        # 生成唯一文件名
        file_extension = os.path.splitext(file.filename)[1]
        unique_filename = f"{uuid.uuid4().hex}_{current_user_id}{file_extension}"
        file_path = os.path.join(documents_dir, unique_filename)
        
        # 保存文件
        with open(file_path, "wb") as f:
            f.write(file_content)
        
        # 生成访问URL
        file_url = f"{settings.static_url_path}/documents/{unique_filename}"
        
        # 文件信息
        file_info = {
            "original_filename": file.filename,
            "filename": unique_filename,
            "file_path": file_path,
            "file_url": file_url,
            "file_size": len(file_content),
            "content_type": file.content_type,
            "uploaded_by": current_user_id,
            "uploaded_at": datetime.now().isoformat()
        }
        
        logger.info(f"用户{current_user_id}上传文档: {file.filename} -> {unique_filename}")
        
        return ResponseModel(
            code=200,
            message="文档上传成功",
            data=file_info
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"上传文档失败: {str(e)}"
        )


@router.get("/files", response_model=ResponseModel[List[Dict[str, Any]]], summary="获取静态文件列表")
async def list_static_files(
    file_type: Optional[str] = Query(None, description="文件类型过滤 (images, documents, exports, templates, assets)"),
    current_user_id: int = Depends(get_current_user_id),
    db: Session = Depends(get_db)
) -> ResponseModel[List[Dict[str, Any]]]:
    """
    获取静态文件列表
    
    - **file_type**: 可选的文件类型过滤
    """
    try:
        static_dir = settings.static_dir_absolute
        
        if not os.path.exists(static_dir):
            return ResponseModel(
                code=200,
                message="静态文件目录不存在",
                data=[]
            )
        
        files_info = []
        
        # 确定要扫描的目录
        if file_type:
            if file_type not in ["images", "documents", "exports", "templates", "assets"]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的文件类型，支持: images, documents, exports, templates, assets"
                )
            scan_dirs = [os.path.join(static_dir, file_type)]
        else:
            scan_dirs = [
                os.path.join(static_dir, "images"),
                os.path.join(static_dir, "documents"),
                os.path.join(static_dir, "exports"),
                os.path.join(static_dir, "templates"),
                os.path.join(static_dir, "assets")
            ]
        
        # 扫描文件
        for scan_dir in scan_dirs:
            if not os.path.exists(scan_dir):
                continue
                
            dir_name = os.path.basename(scan_dir)
            
            for filename in os.listdir(scan_dir):
                file_path = os.path.join(scan_dir, filename)
                
                if os.path.isfile(file_path):
                    stat_info = os.stat(file_path)
                    
                    file_info = {
                        "filename": filename,
                        "file_type": dir_name,
                        "file_url": f"{settings.static_url_path}/{dir_name}/{filename}",
                        "file_size": stat_info.st_size,
                        "created_at": datetime.fromtimestamp(stat_info.st_ctime).isoformat(),
                        "modified_at": datetime.fromtimestamp(stat_info.st_mtime).isoformat()
                    }
                    
                    files_info.append(file_info)
        
        # 按修改时间排序
        files_info.sort(key=lambda x: x["modified_at"], reverse=True)
        
        return ResponseModel(
            code=200,
            message=f"获取静态文件列表成功，共{len(files_info)}个文件",
            data=files_info
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取静态文件列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取静态文件列表失败: {str(e)}"
        )


@router.delete("/files/{file_type}/{filename}", response_model=ResponseModel[None], summary="删除静态文件")
async def delete_static_file(
    file_type: str,
    filename: str,
    current_user_id: int = Depends(get_current_user_id),
    db: Session = Depends(get_db)
) -> ResponseModel[None]:
    """
    删除静态文件
    
    - **file_type**: 文件类型 (images, documents, exports, templates, assets)
    - **filename**: 文件名
    """
    try:
        # 验证文件类型
        if file_type not in ["images", "documents", "exports", "templates", "assets"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的文件类型，支持: images, documents, exports, templates, assets"
            )
        
        # 构建文件路径
        file_path = os.path.join(settings.static_dir_absolute, file_type, filename)
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件不存在"
            )
        
        # 删除文件
        os.remove(file_path)
        
        logger.info(f"用户{current_user_id}删除静态文件: {file_type}/{filename}")
        
        return ResponseModel(
            code=200,
            message="文件删除成功",
            data=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除静态文件失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除静态文件失败: {str(e)}"
        )
