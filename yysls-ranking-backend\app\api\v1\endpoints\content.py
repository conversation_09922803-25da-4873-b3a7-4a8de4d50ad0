"""
内容管理API端点

提供公告、播报消息、静态内容等管理功能
"""
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.content_service import ContentService
from app.services.user_service import UserService
from app.schemas.content import (
    ContentCreate, ContentUpdate, ContentResponse,
    BroadcastMessageCreate, BroadcastMessageResponse
)
from app.schemas.common import ResponseModel, PaginatedResponse
from app.utils.security import verify_token
from app.models.user import UserRole
from app.models.content import ContentType

router = APIRouter()
security = HTTPBearer()
content_service = ContentService()
user_service = UserService()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """获取当前用户对象"""
    payload = verify_token(credentials.credentials)
    user_id = int(payload.get("sub"))
    
    user = user_service.get(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    return user


# 内容管理相关接口
@router.get("/contents", response_model=ResponseModel[PaginatedResponse[ContentResponse]], summary="获取内容列表")
async def get_contents(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页大小"),
    content_type: Optional[ContentType] = Query(None, description="内容类型筛选"),
    is_published: Optional[bool] = Query(None, description="发布状态筛选"),
    search: Optional[str] = Query(None, description="搜索关键词（标题、内容）"),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[PaginatedResponse[ContentResponse]]:
    """
    获取内容列表（需要管理员权限）
    
    - **page**: 页码，从1开始
    - **size**: 每页大小，最大100
    - **content_type**: 内容类型筛选（可选）
    - **is_published**: 发布状态筛选（可选）
    - **search**: 搜索关键词（可选）
    """
    # 检查权限（管理员或超级管理员）
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    try:
        # 构建筛选条件
        filters = {}
        if content_type:
            filters["content_type"] = content_type
        if is_published is not None:
            filters["is_published"] = is_published
        
        # 获取内容列表
        if search:
            contents, total = content_service.search_contents(
                db, search_term=search, skip=(page - 1) * size, limit=size, **filters
            )
        else:
            contents, total = content_service.get_multi_with_total(
                db, skip=(page - 1) * size, limit=size, **filters
            )
        
        # 转换为响应模型
        content_responses = [ContentResponse.model_validate(content) for content in contents]
        
        # 构建分页响应
        pages = (total + size - 1) // size
        paginated_data = PaginatedResponse(
            items=content_responses,
            total=total,
            page=page,
            size=size,
            pages=pages
        )
        
        return ResponseModel(
            code=200,
            message="获取内容列表成功",
            data=paginated_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取内容列表失败: {str(e)}"
        )


@router.post("/contents", response_model=ResponseModel[ContentResponse], summary="创建内容")
async def create_content(
    content_data: ContentCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[ContentResponse]:
    """
    创建新内容（需要管理员权限）
    """
    # 检查权限（管理员或超级管理员）
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    try:
        # 创建内容
        content = content_service.create(db, obj_in=content_data)
        
        return ResponseModel(
            code=200,
            message="创建内容成功",
            data=ContentResponse.model_validate(content)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建内容失败: {str(e)}"
        )


@router.get("/contents/{content_id}", response_model=ResponseModel[ContentResponse], summary="获取内容详情")
async def get_content(
    content_id: int,
    db: Session = Depends(get_db)
) -> ResponseModel[ContentResponse]:
    """
    获取指定内容的详细信息
    """
    try:
        content = content_service.get(db, content_id)
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="内容不存在"
            )
        
        return ResponseModel(
            code=200,
            message="获取内容详情成功",
            data=ContentResponse.model_validate(content)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取内容详情失败: {str(e)}"
        )


@router.put("/contents", response_model=ResponseModel[ContentResponse], summary="更新内容")
async def update_content(
    content_data: ContentUpdate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[ContentResponse]:
    """
    更新内容信息（需要管理员权限）
    """
    # 检查权限（管理员或超级管理员）
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    try:
        # 检查内容是否存在
        existing_content = content_service.get(db, content_data.id)
        if not existing_content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="内容不存在"
            )

        # 更新内容
        content = content_service.update(db, db_obj=existing_content, obj_in=content_data)
        
        return ResponseModel(
            code=200,
            message="更新内容成功",
            data=ContentResponse.model_validate(content)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新内容失败: {str(e)}"
        )


@router.delete("/contents/{content_id}", response_model=ResponseModel[None], summary="删除内容")
async def delete_content(
    content_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[None]:
    """
    删除内容（需要管理员权限）
    """
    # 检查权限（管理员或超级管理员）
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    try:
        # 检查内容是否存在
        content = content_service.get(db, content_id)
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="内容不存在"
            )

        # 删除内容
        content_service.remove(db, id=content_id)
        
        return ResponseModel(
            code=200,
            message="删除内容成功",
            data=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除内容失败: {str(e)}"
        )


# 公开内容接口（不需要认证）
@router.get("/public/announcements", response_model=ResponseModel[List[ContentResponse]], summary="获取公告列表")
async def get_public_announcements(
    limit: int = Query(10, ge=1, le=50, description="获取数量限制"),
    db: Session = Depends(get_db)
) -> ResponseModel[List[ContentResponse]]:
    """
    获取已发布的公告列表（公开接口）
    """
    try:
        announcements = content_service.get_published_announcements_sync(db, limit=limit)
        announcement_responses = [ContentResponse.model_validate(announcement) for announcement in announcements]
        
        return ResponseModel(
            code=200,
            message="获取公告列表成功",
            data=announcement_responses
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取公告列表失败: {str(e)}"
        )


@router.get("/public/about", response_model=ResponseModel[ContentResponse], summary="获取关于我们内容")
async def get_about_content(
    db: Session = Depends(get_db)
) -> ResponseModel[ContentResponse]:
    """
    获取关于我们页面内容（公开接口）
    """
    try:
        about_content = content_service.get_about_content_sync(db)
        if not about_content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="关于我们内容未设置"
            )
        
        return ResponseModel(
            code=200,
            message="获取关于我们内容成功",
            data=ContentResponse.model_validate(about_content)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取关于我们内容失败: {str(e)}"
        )


# 播报消息相关接口
@router.get("/broadcast-messages", response_model=ResponseModel[List[BroadcastMessageResponse]], summary="获取播报消息列表")
async def get_broadcast_messages(
    limit: int = Query(10, ge=1, le=50, description="获取数量限制"),
    is_active: Optional[bool] = Query(None, description="激活状态筛选"),
    db: Session = Depends(get_db)
) -> ResponseModel[List[BroadcastMessageResponse]]:
    """
    获取播报消息列表（公开接口）
    从content表查询type为notify的数据作为播报内容
    """
    try:
        # 构建筛选条件
        filters = {}
        if is_active is not None:
            filters["is_active"] = is_active

        # 从content表查询notify类型的数据
        contents = content_service.get_broadcast_messages_sync(db, limit=limit, **filters)

        # 转换为BroadcastMessageResponse格式
        message_responses = []
        for content in contents:
            message_data = content_service.convert_content_to_broadcast_message(content)
            message_responses.append(BroadcastMessageResponse.model_validate(message_data))

        return ResponseModel(
            code=200,
            message="获取播报消息列表成功",
            data=message_responses
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取播报消息列表失败: {str(e)}"
        )


@router.post("/broadcast-messages", response_model=ResponseModel[BroadcastMessageResponse], summary="创建播报消息")
async def create_broadcast_message(
    message_data: BroadcastMessageCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ResponseModel[BroadcastMessageResponse]:
    """
    创建播报消息（需要管理员权限）
    """
    # 检查权限（管理员或超级管理员）
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    try:
        # 创建播报消息
        message = content_service.create_broadcast_message(db, obj_in=message_data)
        
        return ResponseModel(
            code=200,
            message="创建播报消息成功",
            data=BroadcastMessageResponse.model_validate(message)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建播报消息失败: {str(e)}"
        )
