"""refactor_content_model_to_match_mysql_schema

Revision ID: 006
Revises: 004_ensure_ranking_details_structure
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '006'
down_revision = '004_ensure_ranking_details_structure'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """重构Content模型以匹配MySQL表结构"""
    
    # 1. 添加新字段
    op.add_column('contents', sa.Column('is_published', sa.<PERSON>(), nullable=False, server_default='false', comment='发布状态'))
    op.add_column('contents', sa.Column('publish_at', sa.DateTime(), nullable=True, comment='发布时间'))
    
    # 2. 数据迁移：将status字段的值转换为is_published
    # 将status='published'的记录设置为is_published=true
    op.execute("""
        UPDATE contents 
        SET is_published = true, publish_at = publish_time 
        WHERE status = 'published'
    """)
    
    # 将status='draft'或其他状态的记录设置为is_published=false
    op.execute("""
        UPDATE contents 
        SET is_published = false 
        WHERE status != 'published'
    """)
    
    # 3. 删除不再需要的字段
    op.drop_column('contents', 'summary')
    op.drop_column('contents', 'keywords')
    op.drop_column('contents', 'status')
    op.drop_column('contents', 'is_featured')
    op.drop_column('contents', 'is_top')
    op.drop_column('contents', 'display_order')
    op.drop_column('contents', 'view_count')
    op.drop_column('contents', 'publish_time')
    op.drop_column('contents', 'expire_time')
    op.drop_column('contents', 'created_by')
    op.drop_column('contents', 'updated_by')
    
    # 4. 修改字段约束
    # 修改title字段长度从500改为200
    op.alter_column('contents', 'title',
                    existing_type=sa.VARCHAR(length=500),
                    type_=sa.VARCHAR(length=200),
                    nullable=False,
                    comment='标题')
    
    # 修改content字段为可空
    op.alter_column('contents', 'content',
                    existing_type=sa.TEXT(),
                    nullable=True,
                    comment='内容正文')
    
    # 5. 创建索引
    op.create_index('idx_content_type', 'contents', ['content_type'])
    op.create_index('idx_created_at', 'contents', ['created_at'])
    op.create_index('idx_is_published', 'contents', ['is_published'])
    op.create_index('idx_publish_at', 'contents', ['publish_at'])


def downgrade() -> None:
    """回滚操作"""
    
    # 1. 删除索引
    op.drop_index('idx_publish_at', table_name='contents')
    op.drop_index('idx_is_published', table_name='contents')
    op.drop_index('idx_created_at', table_name='contents')
    op.drop_index('idx_content_type', table_name='contents')
    
    # 2. 恢复字段
    op.add_column('contents', sa.Column('summary', sa.TEXT(), nullable=True, comment='摘要'))
    op.add_column('contents', sa.Column('keywords', sa.VARCHAR(length=500), nullable=True, comment='关键词'))
    op.add_column('contents', sa.Column('status', sa.VARCHAR(length=20), nullable=False, server_default='draft', comment='状态'))
    op.add_column('contents', sa.Column('is_featured', sa.Boolean(), nullable=False, server_default='false', comment='是否推荐'))
    op.add_column('contents', sa.Column('is_top', sa.Boolean(), nullable=False, server_default='false', comment='是否置顶'))
    op.add_column('contents', sa.Column('display_order', sa.Integer(), nullable=False, server_default='0', comment='显示顺序'))
    op.add_column('contents', sa.Column('view_count', sa.Integer(), nullable=False, server_default='0', comment='浏览次数'))
    op.add_column('contents', sa.Column('publish_time', sa.DateTime(), nullable=True, comment='发布时间'))
    op.add_column('contents', sa.Column('expire_time', sa.DateTime(), nullable=True, comment='过期时间'))
    op.add_column('contents', sa.Column('created_by', sa.Integer(), nullable=False, comment='创建人ID'))
    op.add_column('contents', sa.Column('updated_by', sa.Integer(), nullable=True, comment='更新人ID'))
    
    # 3. 数据迁移：将is_published转换回status
    op.execute("""
        UPDATE contents 
        SET status = 'published', publish_time = publish_at 
        WHERE is_published = true
    """)
    
    op.execute("""
        UPDATE contents 
        SET status = 'draft' 
        WHERE is_published = false
    """)
    
    # 4. 恢复字段约束
    op.alter_column('contents', 'title',
                    existing_type=sa.VARCHAR(length=200),
                    type_=sa.VARCHAR(length=500),
                    nullable=False,
                    comment='标题')
    
    op.alter_column('contents', 'content',
                    existing_type=sa.TEXT(),
                    nullable=False,
                    comment='内容')
    
    # 5. 删除新字段
    op.drop_column('contents', 'publish_at')
    op.drop_column('contents', 'is_published')
