# 播报消息接口修改总结

## 修改概述

按照要求，已成功调整播报消息接口 `/broadcast-messages`，修改其查询逻辑从 `broadcast_messages` 表改为从 `content` 表中查询类型为 "notify" 的数据作为播报内容。

## 具体修改内容

### 1. 模型层修改 (`app/models/content.py`)

- **添加新的内容类型**：在 `ContentType` 枚举中添加了 `NOTIFY = "notify"` 类型
- **位置**：第16行

```python
class ContentType(str, Enum):
    """内容类型枚举"""
    BROADCAST = "broadcast"      # 播报信息
    NOTIFY = "notify"           # 通知播报  ← 新添加
    ANNOUNCEMENT = "announcement"  # 公告
    ABOUT = "about"             # 关于我们
    HELP = "help"               # 帮助文档
    PRIVACY = "privacy"         # 隐私政策
    TERMS = "terms"             # 服务条款
```

### 2. 服务层修改 (`app/services/content_service.py`)

#### 2.1 修改查询逻辑
- **方法**：`get_broadcast_messages_sync()` (新增同步版本)
- **查询目标**：从 `content` 表查询 `content_type = "notify"` 的数据
- **过滤逻辑**：将 `is_active` 参数映射到 `is_published` 字段
- **排序**：按 `publish_at` 和 `created_at` 降序排列

#### 2.2 数据转换适配器
- **方法**：`convert_content_to_broadcast_message()`
- **功能**：将 `Content` 对象转换为 `BroadcastMessageResponse` 格式
- **字段映射**：
  - `content.is_published` → `is_active`
  - `content.publish_at` → `start_time`
  - `content.content_type` → `message_type` (固定为 "notify")
  - 其他字段设置合理默认值

```python
def convert_content_to_broadcast_message(self, content: Content) -> Dict[str, Any]:
    """将Content对象转换为BroadcastMessageResponse格式的字典"""
    return {
        "id": content.id,
        "title": content.title,
        "content": content.content or "",
        "message_type": "notify",  # 固定为notify类型
        "priority": 0,  # 默认优先级
        "is_active": content.is_published,  # 映射发布状态到激活状态
        "start_time": content.publish_at,  # 映射发布时间到开始时间
        "end_time": None,  # 默认无结束时间
        "display_duration": 5,  # 默认显示5秒
        "target_audience": None,  # 默认无目标受众
        "created_by": None,  # Content表没有创建者字段
        "updated_by": None,  # Content表没有更新者字段
        "created_at": content.created_at,
        "updated_at": content.updated_at
    }
```

### 3. API层修改 (`app/api/v1/endpoints/content.py`)

#### 3.1 接口逻辑调整
- **接口**：`GET /broadcast-messages`
- **查询源**：从 `broadcast_messages` 表改为 `content` 表
- **响应格式**：保持 `ResponseModel[List[BroadcastMessageResponse]]` 不变
- **数据转换**：使用适配器将 `Content` 数据转换为 `BroadcastMessageResponse` 格式

```python
# 从content表查询notify类型的数据
contents = content_service.get_broadcast_messages_sync(db, limit=limit, **filters)

# 转换为BroadcastMessageResponse格式
message_responses = []
for content in contents:
    message_data = content_service.convert_content_to_broadcast_message(content)
    message_responses.append(BroadcastMessageResponse.model_validate(message_data))
```

## 兼容性说明

### 1. 响应格式兼容
- 保持原有的 `BroadcastMessageResponse` 响应模型不变
- 通过数据转换确保字段完整性和类型正确性

### 2. 查询参数兼容
- 保持原有的 `is_active` 查询参数，内部映射到 `is_published` 字段
- 保持原有的 `limit` 参数功能

### 3. 业务逻辑兼容
- 新的查询逻辑按发布时间排序，符合播报消息的时效性要求
- 只返回已发布的内容（`is_published = true`）

## 使用说明

### 1. 创建播报内容
要创建播报消息，需要在 `content` 表中插入 `content_type = "notify"` 的记录：

```sql
INSERT INTO contents (content_type, title, content, is_published, publish_at) 
VALUES ('notify', '播报标题', '播报内容', true, NOW());
```

### 2. 查询播报消息
接口调用方式保持不变：

```http
GET /api/v1/broadcast-messages?limit=10&is_active=true
```

### 3. 响应数据格式
响应格式保持不变，包含所有 `BroadcastMessageResponse` 字段。

## 注意事项

1. **数据迁移**：如果现有系统中 `broadcast_messages` 表有数据，需要考虑数据迁移到 `content` 表
2. **权限控制**：播报内容的创建和管理通过现有的内容管理接口进行
3. **缓存更新**：如果有缓存机制，需要确保缓存键的一致性
4. **监控告警**：建议添加监控确保新的查询逻辑正常工作

## 测试建议

1. **功能测试**：验证接口返回正确的 notify 类型内容
2. **兼容性测试**：确保响应格式与原接口完全一致
3. **性能测试**：验证新的查询逻辑性能表现
4. **边界测试**：测试空数据、大量数据等边界情况
