#!/bin/bash

# 燕友圈榜单系统 - 上传目录准备脚本
# 用于在部署前创建必要的上传目录并设置正确的权限

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warning "检测到以root用户运行，建议使用普通用户"
    fi
}

# 创建上传目录
create_upload_dirs() {
    log_info "创建上传目录..."
    
    # 基础目录
    local upload_base="./uploads"
    local static_base="./static"

    # 需要创建的目录列表
    local dirs=(
        # 上传目录
        "$upload_base"
        "$upload_base/temp"
        "$upload_base/temp/excel"
        "$upload_base/rankings"
        "$upload_base/avatars"
        "$upload_base/exports"
        # 静态文件目录
        "$static_base"
        "$static_base/images"
        "$static_base/documents"
        "$static_base/exports"
        "$static_base/templates"
        "$static_base/assets"
    )
    
    local created_count=0
    local failed_count=0
    
    for dir in "${dirs[@]}"; do
        if mkdir -p "$dir" 2>/dev/null; then
            # 设置目录权限为755
            chmod 755 "$dir" 2>/dev/null || log_warning "无法设置目录权限: $dir"
            log_success "创建目录: $dir"
            ((created_count++))
        else
            log_error "创建目录失败: $dir"
            ((failed_count++))
        fi
    done
    
    log_info "目录创建完成: 成功 $created_count 个，失败 $failed_count 个"
    
    if [[ $failed_count -gt 0 ]]; then
        return 1
    fi
    
    return 0
}

# 检查目录权限
check_permissions() {
    log_info "检查目录权限..."
    
    local upload_base="./uploads"
    local static_base="./static"

    # 检查上传目录
    if [[ ! -d "$upload_base" ]]; then
        log_error "上传目录不存在: $upload_base"
        return 1
    fi

    if [[ ! -r "$upload_base" ]] || [[ ! -w "$upload_base" ]]; then
        log_error "上传目录权限不足: $upload_base"
        return 1
    fi

    log_success "上传目录权限检查通过: $upload_base"

    # 检查静态文件目录
    if [[ ! -d "$static_base" ]]; then
        log_error "静态文件目录不存在: $static_base"
        return 1
    fi

    if [[ ! -r "$static_base" ]] || [[ ! -w "$static_base" ]]; then
        log_error "静态文件目录权限不足: $static_base"
        return 1
    fi

    log_success "静态文件目录权限检查通过: $static_base"
    return 0
}

# 创建测试文件
test_file_operations() {
    log_info "测试文件操作..."
    
    local test_dir="./uploads/temp/excel"
    local test_file="$test_dir/test_file.txt"
    
    # 创建测试文件
    if echo "test content" > "$test_file" 2>/dev/null; then
        log_success "文件写入测试通过"
        
        # 删除测试文件
        if rm "$test_file" 2>/dev/null; then
            log_success "文件删除测试通过"
        else
            log_warning "文件删除测试失败"
        fi
    else
        log_error "文件写入测试失败"
        return 1
    fi
    
    return 0
}

# 显示目录信息
show_directory_info() {
    log_info "目录信息:"
    
    local upload_base="./uploads"
    local static_base="./static"

    echo "📁 目录结构:"

    if [[ -d "$upload_base" ]]; then
        echo "  上传目录:"
        find "$upload_base" -type d | sort | while read -r dir; do
            local perms=$(stat -c "%a" "$dir" 2>/dev/null || echo "???")
            echo "    $dir (权限: $perms)"
        done
    else
        log_warning "  上传目录不存在"
    fi

    if [[ -d "$static_base" ]]; then
        echo "  静态文件目录:"
        find "$static_base" -type d | sort | while read -r dir; do
            local perms=$(stat -c "%a" "$dir" 2>/dev/null || echo "???")
            echo "    $dir (权限: $perms)"
        done
    else
        log_warning "  静态文件目录不存在"
    fi

    echo ""
    echo "📊 目录统计:"

    if [[ -d "$upload_base" ]]; then
        local upload_dir_count=$(find "$upload_base" -type d | wc -l)
        local upload_size=$(du -sh "$upload_base" 2>/dev/null | cut -f1 || echo "未知")
        echo "  上传目录数量: $upload_dir_count"
        echo "  上传目录大小: $upload_size"
    fi

    if [[ -d "$static_base" ]]; then
        local static_dir_count=$(find "$static_base" -type d | wc -l)
        local static_size=$(du -sh "$static_base" 2>/dev/null | cut -f1 || echo "未知")
        echo "  静态目录数量: $static_dir_count"
        echo "  静态目录大小: $static_size"
    fi
}

# 主函数
main() {
    echo "🚀 燕友圈榜单系统 - 上传目录准备"
    echo "=================================="
    echo ""
    
    # 检查用户权限
    check_root
    
    # 创建目录
    if ! create_upload_dirs; then
        log_error "目录创建失败，请检查权限"
        exit 1
    fi
    
    echo ""
    
    # 检查权限
    if ! check_permissions; then
        log_error "权限检查失败"
        exit 1
    fi
    
    echo ""
    
    # 测试文件操作
    if ! test_file_operations; then
        log_error "文件操作测试失败"
        exit 1
    fi
    
    echo ""
    
    # 显示目录信息
    show_directory_info
    
    echo ""
    log_success "🎉 上传目录准备完成！"
    echo ""
    echo "💡 提示:"
    echo "  - 如果在Docker环境中运行，请确保卷映射配置正确"
    echo "  - 建议定期清理临时文件目录"
    echo "  - 生产环境建议设置文件上传大小限制"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
