"""
应用配置模块
"""
from typing import List, Optional, Union

from pydantic import field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    app_name: str = "燕友圈榜单系统"
    app_version: str = "1.0.0"
    debug: bool = False
    secret_key: str
    
    # 数据库配置
    database_url: str
    database_url_async: str
    
    # JWT配置
    jwt_secret_key: str
    jwt_algorithm: str = "HS256"
    access_token_expire_minutes: int = 20160
    
    # 微信登录配置
    wechat_app_id: str
    wechat_app_secret: str

    # 前端配置
    frontend_url: str = "http://localhost:3000"

    # Redis配置
    redis_url: Optional[str] = None
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "logs/app.log"
    
    # CORS配置
    allowed_origins: Union[str, List[str]] = []

    # 文件上传配置
    upload_dir: str = "uploads"
    max_file_size: int = 10485760  # 10MB

    @property
    def upload_dir_absolute(self) -> str:
        """获取上传目录的绝对路径"""
        import os
        if os.path.isabs(self.upload_dir):
            return self.upload_dir
        return os.path.abspath(self.upload_dir)

    @field_validator("allowed_origins", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v):
        if isinstance(v, str) and v:
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, str) and not v:
            return []
        return v if isinstance(v, list) else []
    
    model_config = {
        "env_file": ".env",
        "case_sensitive": False,
        "extra": "ignore"
    }


# 全局配置实例
settings = Settings()
